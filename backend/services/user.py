from typing import Dict, Any
from datetime import datetime, UTC

from sqlalchemy import or_
from sqlalchemy.orm import Session

from models.user import User, Status, Gender
from schemas.user import UserSchema


class UserService:
  def create(self, session: Session, payload: Dict[str, Any]) -> User | None:
    if schema := self._convert2schema(payload):
      user = User(**schema.model_dump())
      user.save(session)
      return user

  def get(
    self, session: Session, userid: str | None = None, email: str | None = None
  ) -> User | None:
    return (
      session.query(User)
      .filter(or_(User.userid == userid, User.email == email))
      .first()
    )

  def update(
    self, session: Session, userid: str, payload: Dict[str, Any]
  ) -> User | None:
    if user := self.get(session, userid):
      if schema := self._convert2schema(payload):
        for key, val in vars(schema).items():
          setattr(user, key, val) if val else None
          user.updated_at = datetime.now(tz=UTC)
        user.save(session)
        return user

  def _convert2schema(self, payload: Dict[str, Any]) -> UserSchema | None:
    if userid := payload.get("userid"):
      print(payload)
      return UserSchema(
        userid=userid,
        name=payload.get("name", ""),
        email=payload.get("email", ""),
        gender=Gender(int(payload.get("gender", 0))),
        status=Status(int(payload.get("status", 4))),
        # department=payload.get("department", ""),
        alias=payload.get("alias", ""),
        mobile=payload.get("mobile", ""),
        position=payload.get("position", ""),
        avatar=payload.get("avatar", ""),
      )


user_service = UserService()
