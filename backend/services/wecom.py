from typing import Dict, Any
from dataclasses import dataclass
from pathlib import Path

from httpx import Client

from core.settings import get_settings


@dataclass
class WecomService:
  corp_id: str
  corp_secret: str
  agent_id: str
  access_token_file: Path
  base_url: str = "https://qyapi.weixin.qq.com/cgi-bin"

  def get_authorize_url(
    self, redirect_url: str | None = "", state: str | None = "Login"
  ) -> str:
    return f"https://open.weixin.qq.com/connect/oauth2/authorize?appid={self.corp_id}&redirect_url={redirect_url}&state={state}&response_type=code&scope=snsapi_privateinfo&agentid={self.agent_id}#wechat_redirect"

  def get_user(self, code: str) -> Dict[str, Any]:
    userinfo = self._get("auth/getuserinfo", params={"code": code})
    userid = userinfo.get("userid", "")
    user = self._get("user/get", params={"userid": userid})
    if user_ticket := userinfo.get("user_ticket", ""):
      user_detail = self._post("auth/getuserdetail", json={"user_ticket": user_ticket})
      return user_detail | user
    return user

  def get_access_token(self) -> str:
    if self.access_token_file.exists():
      with open(self.access_token_file, "r") as f:
        return f.readline().strip()
    return self._get_new_access_token()

  def _get(self, url, params: Dict[str, Any] = {}) -> Dict[str, Any]:
    return self._request("GET", url, params)

  def _post(
    self, url, params: Dict[str, Any] = {}, json: Dict[str, Any] = {}
  ) -> Dict[str, Any]:
    return self._request("POST", url, params, json)

  def _get_new_access_token(self) -> str:
    with self._client() as client:
      r = client.get(
        "gettoken", params={"corpid": self.corp_id, "corpsecret": self.corp_secret}
      )
      if r.status_code != 200:
        raise ValueError(f"HTTP Error: {r.status_code}")

      result = r.json()
      if result["errcode"] != 0:
        raise ValueError(f"Err Code: {result['errcode']} {result['errmsg']}")

      access_token = result["access_token"]
      with open(self.access_token_file, "w") as f:
        f.write(access_token)

      return access_token

  def _client(self) -> Client:
    return Client(base_url=self.base_url, headers={"Content-Type": "application/json"})

  def _request(
    self,
    method: str,
    url: str,
    params: Dict[str, Any] = {},
    json: Dict[str, Any] = {},
  ) -> Dict[str, Any]:
    params.setdefault("access_token", self.get_access_token())
    with self._client() as client:
      r = client.request(method, url, params=params, json=json)
      if r.status_code != 200:
        raise ValueError(f"HTTP Error: {r.text}")

      result = r.json()
      errcode = result["errcode"]
      # access_token 错误或过期，需要重新获取
      if errcode == 40014 or errcode == 41001 or errcode == 42001:
        self._get_new_access_token()
        return self._request(method, url, params, json)

      if result["errcode"] != 0:
        raise ValueError(f"Err Code: {result['errcode']} {result['errmsg']}")

      return result


settings = get_settings()
wecom_service = WecomService(
  corp_id=settings.wecom_corp_id,
  corp_secret=settings.wecom_corp_secret,
  agent_id=settings.wecom_agent_id,
  access_token_file=settings.state / "access_token",
)

if __name__ == "__main__":
  # wecom = Wecom(
  #   corp_id="wwe57e9e46997cd816",
  #   corp_secret="8hJ3FUQuZUfE2xeHjIJnRsWiThxKwwVgIM-2L8cJUE8",
  #   agent_id="1000161",
  #   state=Path("./state"),
  # )
  # 测试企业
  wecom = WecomService(
    corp_id="ww98e6f25c9311c20b",
    corp_secret="8hJ3FUQuZUfE2xeHjIJnRsWiThxKwwVgIM-2L8cJUE8",
    agent_id="1000002",
    access_token_file=Path("state/access_token"),
  )
