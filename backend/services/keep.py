from typing import Any, Dict
from dataclasses import dataclass

from httpx import Client

from core.settings import get_settings


@dataclass
class Keep:
  api_url: str
  api_key: str

  def create_user(self, username: str, password: str) -> str | None:
    result = self._post("auth/users", json={"username": username, "password": password})
    print(result)

  def get_incident(self, incident_id: str):
    return self._get(f"incidents/{incident_id}")

  def get_incidents(self, cel: str = "") -> dict | None:
    with self._client() as client:
      r = client.get("incidents", params={"cel": cel})
      if r.status_code == 200:
        return r.json()

  def get_incident_alerts(self, incident_id: str) -> dict | None:
    with self._client() as client:
      r = client.get(f"incidents/{incident_id}/alerts")
      if r.status_code == 200:
        return r.json()

  def _client(self):
    return Client(
      base_url=self.api_url,
      headers={"X-API-KEY": self.api_key, "Content-Type": "application/json"},
    )

  def _request(
    self, method: str, url: str, params: Dict[str, Any] = {}, json: Dict[str, Any] = {}
  ) -> Dict[str, Any] | str:
    with self._client() as client:
      r = client.request(method, url, params=params, json=json)
      if r.status_code != 200:
        raise ValueError(f"HTTP Error: {r.text}")
      try:
        return r.json()
      except Exception:
        return r.text

  def _get(self, url: str, params: Dict[str, Any] = {}) -> Dict[str, Any] | str:
    return self._request("GET", url, params)

  def _post(
    self, url: str, params: Dict[str, Any] = {}, json: Dict[str, Any] = {}
  ) -> Dict[str, Any] | str:
    return self._request("POST", url, params, json=json)


settings = get_settings()
keep_service = Keep(api_url=settings.keep_api_url, api_key=settings.keep_api_key)

if __name__ == "__main__":
  keep = Keep(
    api_url="https://keep-hk.paykka.com/v2",
    api_key="045e9bc1-8349-4d37-a599-fd80245b9382",
  )
  alerts = keep.get_incident_alerts(incident_id="1b6a5efa-2b3c-4635-8027-de3a973186a2")
  print(alerts)
