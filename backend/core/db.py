from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base


engine = create_engine("sqlite:///./test.db")
SessionLocal = sessionmaker(autoflush=False, bind=engine)
Base = declarative_base()


# TODO 后续放到迁移工具做
def init_db():
  from models.user import User

  Base.metadata.create_all(engine)


def get_db():
  db = SessionLocal()
  try:
    yield db
  finally:
    db.close()
