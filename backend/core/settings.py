from functools import lru_cache
from pathlib import Path

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
  model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")

  state: Path = Field(default=Path("state"), description="用于存放临时状态文件")
  jwt_secret_key: str = Field(default="secretkey", description="用于处理 JWT 令牌签名")
  jwt_algorithm: str = Field(default="HS256", description="JWT 签名算法")
  wecom_corp_id: str = Field(default="", description="企业微信组织 ID")
  wecom_corp_secret: str = Field(default="", description="企业微信自建应用 Secret")
  wecom_agent_id: str = Field(default="", description="企业微信自建应用 ID")
  keep_api_url: str = Field(default="", description="Keep 平台 API 链接")
  keep_api_key: str = Field(default="", description="Keep 平台 API 密钥")


@lru_cache
def get_settings():
  return Settings()


if __name__ == "__main__":
  settings = get_settings()
  print(settings)
