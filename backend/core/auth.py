from typing import Dict, Any, Annotated
from datetime import timedelta, datetime, UTC

from fastapi import Depends, HTTPException
from fastapi.security import OAuth2<PERSON>asswordBearer
from starlette.status import HTTP_401_UNAUTHORIZED
from sqlalchemy.orm import Session
from passlib.context import CryptContext
import jwt

from core.settings import get_settings
from core.db import get_db
from services.user import user_service

settings = get_settings()
SECRET_KEY = settings.jwt_secret_key
ALGORITHM = settings.jwt_algorithm

oauth2_schema = OAuth2PasswordBearer(tokenUrl="/api/v1/login")
pwd_context = CryptContext(schemes=["bcrypt"])


def verify_password(plain_password: str, hashed_password: str):
  return pwd_context.verify(plain_password, hashed_password)


def hash_password(password: str):
  return pwd_context.hash(password)


def authenticat_user(session: Session, email: str, password: str):
  if user := user_service.get(session, email):
    if verify_password(password, user.password):
      return user
  return False


def create_access_token(
  payload: Dict[str, Any], expires_delta: timedelta = timedelta(days=30)
) -> str:
  to_encode = payload.copy()
  exp = datetime.now(tz=UTC) + expires_delta
  to_encode.update({"exp": exp})
  return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)


def verify_token(token: str):
  try:
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    return payload
  except Exception:
    return None


def get_current_user(
  token: Annotated[str, Depends(oauth2_schema)],
  session: Annotated[Session, Depends(get_db)],
):
  if payload := verify_token(token):
    if userid := payload.get("userid", ""):
      if user := user_service.get(session, userid):
        return user

  raise HTTPException(
    status_code=HTTP_401_UNAUTHORIZED,
    detail="Not authenticated",
    headers={"WWW-Authenticate": "Bearer"},
  )
