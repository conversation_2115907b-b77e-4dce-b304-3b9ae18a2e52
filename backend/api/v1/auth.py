from fastapi import APIRouter
from fastapi.responses import RedirectResponse

from core.deps import SessionDep
from core.auth import create_access_token, authenticat_user
from services.wecom import wecom_service
from services.user import user_service
from schemas.auth import WecomLoginSchema, LoginSchema


router = APIRouter()


# @router.post("/login")
# def login(payload: LoginSchema, session: SessionDep):
#   email = payload.email
#   password = payload.password
#   # if user := authenticat_user(session, email, password):
#   #   token = create_access_token()


@router.get("/wecom/authorize", response_class=RedirectResponse)
def wecom_authorize(redirect_url: str | None = "", state: str | None = "LOGIN"):
  return wecom_service.get_authorize_url(redirect_url, state)


@router.get("/wecom/config")
def wecom_config():
  return {
    "wecom_corp_id": wecom_service.corp_id,
    "wecom_agent_id": wecom_service.agent_id,
  }


@router.post("/wecom/login")
def wecom_login(payload: WecomLoginSchema, session: SessionDep):
  code = payload.code
  user_data = wecom_service.get_user(code)
  print(user_data)
  user = user_service.get(session, user_data.get("userid", ""))
  print(user)
  if user is None:
    user = user_service.create(session, user_data)
  else:
    user = user_service.update(session, user.userid, user_data)
  token = create_access_token(
    {
      "userid": user.userid,
      "name": user.name,
      "avatar": user.avatar,
    }
  )
  return {"token": token}
