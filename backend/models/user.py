from enum import IntEnum

from sqlalchemy import Integer
from sqlalchemy.orm import Mapped, mapped_column

from models.base import BaseModel


class Gender(IntEnum):
  UNKOWN = 0
  MALE = 1
  FEMALE = 2


class Status(IntEnum):
  ACTIVATED = 1  # 已激活（已激活企业微信或已关注微信插件）
  DISABLED = 2  # 已禁用
  INACTIVE = 4  # 未激活（既未激活企业微信又未关注微信插件）
  RESIGNED = 5  # 已退出企业


class User(BaseModel):
  userid: Mapped[str] = mapped_column(
    nullable=False, unique=True, comment="企业微信 userid"
  )
  gender: Mapped[Gender] = mapped_column(
    Integer, nullable=True, default=Gender.UNKOWN, comment="性别"
  )
  name: Mapped[str] = mapped_column(nullable=False, comment="姓名")
  status: Mapped[Status] = mapped_column(
    Integer, nullable=False, comment="企微用户激活状态"
  )
  alias: Mapped[str] = mapped_column(nullable=True, default="", comment="别名")
  email: Mapped[str] = mapped_column(nullable=True, default="", comment="邮箱")
  mobile: Mapped[str] = mapped_column(nullable=True, default="", comment="手机号")
  position: Mapped[str] = mapped_column(nullable=True, default="", comment="职务")
  department: Mapped[str] = mapped_column(nullable=True, default="", comment="部门")
  avatar: Mapped[str] = mapped_column(nullable=True, default="", comment="企微头像")
  password: Mapped[str] = mapped_column(nullable=True, default="", comment="用户密码")

  def __repr__(self) -> str:
    return f"<User userid={self.userid}, name={self.name}>"
