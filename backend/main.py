from contextlib import asynccontextmanager

from fastapi import FastAPI

from api import v1
from core.db import init_db
from core.settings import get_settings


@asynccontextmanager
async def lifespan(app: FastAPI):
  # TODO 后续放到迁移工具做
  init_db()
  # 创建 state 目录
  settings = get_settings()
  if not settings.state.exists():
    settings.state.mkdir()
  yield


app = FastAPI(
  lifespan=lifespan,
  title="PayKKa Duty",
  docs_url="/api/v1/docs",
  openapi_url="/api/v1/openapi.json",
)
app.include_router(v1.router, prefix="/api/v1")
