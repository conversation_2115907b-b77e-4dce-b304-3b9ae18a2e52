import { createFileRoute } from '@tanstack/react-router'
import { useAuth } from '@/auth'

export const Route = createFileRoute('/_auth/my')({
  component: MyPageComponent,
})

function MyPageComponent() {
  const auth = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">我的信息</h1>
          </div>

          <div className="px-6 py-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">用户ID</label>
                <p className="mt-1 text-sm text-gray-900">{auth.user?.userid || '未知'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">邮箱</label>
                <p className="mt-1 text-sm text-gray-900">{auth.user?.email || '未知'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">姓名</label>
                <p className="mt-1 text-sm text-gray-900">{auth.user?.name || '未知'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">认证状态</label>
                <p className="mt-1 text-sm text-gray-900">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${auth.isAuthenticated
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                    }`}>
                    {auth.isAuthenticated ? '已认证' : '未认证'}
                  </span>
                </p>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={auth.logout}
                className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
