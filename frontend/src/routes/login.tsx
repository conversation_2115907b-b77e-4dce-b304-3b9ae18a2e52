import { createFileRoute, useRouter } from '@tanstack/react-router'
import { z } from "zod"
import { useAuth } from '@/auth'
import { useEffect, useState } from 'react'

const loginSearchSchema = z.object({
  redirectUrl: z.string().catch("")
})

export const Route = createFileRoute('/login')({
  validateSearch: (search) => loginSearchSchema.parse(search),
  beforeLoad: async () => {
    // 检查是否已经登录，如果已登录则重定向
    const token = localStorage.getItem('access_token');
    if (token) {
      // TODO: 验证 token 有效性
      // 如果已登录，可以重定向到首页或指定页面
    }
  },
  component: LoginComponent,
})

function LoginComponent() {
  const { redirectUrl } = Route.useSearch();
  const auth = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 如果已经认证，自动重定向
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      const targetUrl = redirectUrl || '/';
      router.navigate({ to: targetUrl });
    }
  }, [auth.isAuthenticated, auth.isLoading, redirectUrl, router]);

  const handleWecomLogin = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 构建企业微信授权 URL
      const authUrl = `/api/v1/auth/wecom/authorize?redirect_url=${encodeURIComponent(window.location.origin + '/login')}&state=LOGIN`;

      // 重定向到企业微信授权页面
      window.location.href = authUrl;
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理企业微信回调
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state === 'LOGIN') {
      auth.login(code, state).catch((err) => {
        setError(err instanceof Error ? err.message : '登录失败');
      });
    }
  }, [auth]);

  if (auth.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">正在验证登录状态...</div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">PayKKa Duty</h2>
          <p className="mt-2 text-sm text-gray-600">
            请使用企业微信登录
          </p>
          {redirectUrl && (
            <p className="mt-1 text-xs text-gray-500">
              登录后将跳转到: {redirectUrl}
            </p>
          )}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <button
            onClick={handleWecomLogin}
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '正在跳转...' : '企业微信登录'}
          </button>
        </div>
      </div>
    </div>
  );
}
