import { createFileRoute } from '@tanstack/react-router'
import { z } from "zod"


const loginSearchSchema = z.object({
  redirectUrl: z.string().catch("")
})

export const Route = createFileRoute('/login')({
  validateSearch: (search) => loginSearchSchema.parse(search),
  component: RouteComponent,
})

function RouteComponent() {
  const { redirectUrl } = Route.useSearch()

  return <div>{redirectUrl} Hello "/login"!</div>
}
