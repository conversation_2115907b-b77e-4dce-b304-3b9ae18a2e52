import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useAuth } from '@/auth'
import { Button } from '@/components/ui/button'

export const Route = createFileRoute('/')({
  component: Home,
})

function Home() {
  const auth = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">PayKKa Duty</h1>
            </div>

            <div className="flex items-center space-x-4">
              {auth.isAuthenticated ? (
                <>
                  <span className="text-sm text-gray-700">
                    欢迎, {auth.user?.name || auth.user?.email}
                  </span>
                  <Link
                    to="/my"
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    我的信息
                  </Link>
                  <Button
                    onClick={auth.logout}
                    variant="outline"
                    size="sm"
                  >
                    退出登录
                  </Button>
                </>
              ) : (
                <Link to="/login" search={{ redirectUrl: "" }}>
                  <Button size="sm">
                    登录
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              欢迎使用 PayKKa Duty
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              企业级值班管理系统
            </p>

            {auth.isAuthenticated ? (
              <div className="space-y-4">
                <p className="text-green-600 font-medium">
                  您已成功登录！
                </p>
                <div className="space-x-4">
                  <Link to="/my">
                    <Button>
                      查看我的信息
                    </Button>
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-gray-600">
                  请先登录以使用系统功能
                </p>
                <Link to="/login" search={{ redirectUrl: "" }}>
                  <Button size="lg">
                    立即登录
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}