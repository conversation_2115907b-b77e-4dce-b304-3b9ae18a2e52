import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'
import { useAuth } from '@/auth'

export const Route = createFileRoute('/_auth')({
  beforeLoad: async ({ location }) => {
    // 检查 localStorage 中是否有 token
    const token = localStorage.getItem('access_token');

    if (!token) {
      // 如果没有 token，重定向到登录页面，并保存当前路径
      throw redirect({
        to: '/login',
        search: {
          redirectUrl: location.href,
        },
      });
    }

    // TODO: 这里可以添加 token 验证逻辑
    // 如果 token 无效，也应该重定向到登录页面
  },
  component: AuthLayout,
})

function AuthLayout() {
  const auth = useAuth();

  // 如果正在加载认证状态，显示加载指示器
  if (auth.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  // 如果未认证，显示未授权信息（这种情况理论上不应该发生，因为 beforeLoad 会重定向）
  if (!auth.isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-red-600">未授权访问</div>
      </div>
    );
  }

  // 如果已认证，渲染子路由
  return <Outlet />;
}
