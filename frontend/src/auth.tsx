import React, { createContext, useContext, useEffect, useState } from "react";

const ACCESS_TOKEN_KEY = "access_token";

export interface User {
  userid: string;
  email: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  login(code: string, state: string): Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthState | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem(ACCESS_TOKEN_KEY);
    if (token) {
      // TODO: 对 token 进行校验
      setUser({ userid: "1", email: "1" });
      setIsAuthenticated(true);
    }
  })

  const login = async (code: string, state: string) => { };
  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem(ACCESS_TOKEN_KEY);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context;
}