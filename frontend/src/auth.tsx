import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "@tanstack/react-router";

const ACCESS_TOKEN_KEY = "access_token";

export interface User {
  userid: string;
  email: string;
  name?: string;
  avatar?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  login(code: string, state: string): Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthState | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = localStorage.getItem(ACCESS_TOKEN_KEY);
        if (token) {
          // TODO: 验证 token 并获取用户信息
          // 这里暂时使用模拟数据
          setUser({ userid: "1", email: "<EMAIL>", name: "Test User" });
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error("认证初始化失败:", error);
        localStorage.removeItem(ACCESS_TOKEN_KEY);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // 更新路由上下文
  useEffect(() => {
    if (!isLoading) {
      router.invalidate();
    }
  }, [isAuthenticated, isLoading, router]);

  const login = async (code: string, state: string) => {
    try {
      setIsLoading(true);
      // TODO: 实现企业微信登录逻辑
      const response = await fetch('/api/v1/auth/wecom/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, state }),
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem(ACCESS_TOKEN_KEY, data.token);
        // TODO: 解析 token 获取用户信息
        setUser({ userid: "1", email: "<EMAIL>", name: "Test User" });
        setIsAuthenticated(true);
      } else {
        throw new Error('登录失败');
      }
    } catch (error) {
      console.error("登录失败:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    router.invalidate();
  };

  const authState: AuthState = {
    isAuthenticated,
    user,
    login,
    logout,
    isLoading,
  };

  return (
    <AuthContext.Provider value={authState}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context;
}