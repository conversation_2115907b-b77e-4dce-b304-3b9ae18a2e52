{"name": "frontend", "version": "1.0.0", "scripts": {"dev": "vite dev", "build": "vite build"}, "type": "module", "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-router": "^1.131.8", "@tanstack/react-start": "^1.131.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vite": "^7.1.2", "zod": "^4.0.17"}, "devDependencies": {"@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4"}}